/**
 * @file src/renderer/components/FileTree.tsx
 * @description Ultra-optimized file tree with Tailwind v4, GPU acceleration, and advanced virtualization
 * Performance features: Fixed-height virtualization, GPU layers, optimized scrolling, smart caching
 */
import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import type { Project, FileTreeNode } from '../../shared/ipc.d';
import '../styles/globals.css';

// ========================
// Types & Interfaces
// ========================
interface TreeNodeData extends FileTreeNode {
  level: number;
  isExpanded: boolean;
  parentId: string | null;
  isLoading?: boolean;
}

type CompactMode = 'normal' | 'compact' | 'ultra-compact';

interface FileTreeProps {
  project: Project | null;
  onSelectionChange: (selected: Set<string>) => void;
  compactMode?: CompactMode; // Different levels of compactness
  showCheckboxes?: boolean; // Optional prop to show/hide checkboxes
}

interface TreeNodeProps {
  node: TreeNodeData;
  style: React.CSSProperties;
  onToggle: (node: TreeNodeData) => void;
  onSelect: (node: TreeNodeData) => void;
  selectionState: 'all' | 'none' | 'some';
  rowIndex: number;
  isActive: boolean;
  loadingNodes: Set<string>;
  compactMode: CompactMode;
  showCheckboxes: boolean;
}

// ========================
// Performance Utilities & Hooks
// ========================

// Ultra-fast debounced callback with RAF optimization
function useDebouncedCallback<T extends any[]>(
  callback: (...args: T) => void,
  delay: number
) {
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const rafRef = useRef<number | null>(null);

  return useCallback(
    (...args: T) => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      if (rafRef.current) cancelAnimationFrame(rafRef.current);

      timeoutRef.current = setTimeout(() => {
        rafRef.current = requestAnimationFrame(() => callback(...args));
      }, delay);
    },
    [callback, delay]
  );
}

// Optimized localStorage hook with error boundaries
function useLocalStorage<T>(key: string, defaultValue: T) {
  const [value, setValue] = useState<T>(() => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch {
      return defaultValue;
    }
  });

  const setStoredValue = useCallback(
    (newValue: T | ((prev: T) => T)) => {
      setValue((prev) => {
        const valueToStore = typeof newValue === 'function' ? (newValue as (prev: T) => T)(prev) : newValue;
        try {
          localStorage.setItem(key, JSON.stringify(valueToStore));
        } catch (error) {
          console.warn('Failed to save to localStorage:', error);
        }
        return valueToStore;
      });
    },
    [key]
  );

  return [value, setStoredValue] as const;
}

// Smart cache for node data with LRU eviction
class NodeCache {
  private cache = new Map<string, any>();
  private maxSize = 1000;

  get(key: string) {
    const value = this.cache.get(key);
    if (value) {
      // Move to end (most recently used)
      this.cache.delete(key);
      this.cache.set(key, value);
    }
    return value;
  }

  set(key: string, value: any) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.maxSize) {
      // Remove least recently used
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  clear() {
    this.cache.clear();
  }
}

const nodeCache = new NodeCache();

// Compact icon system with miniature SVGs
const iconCache = new Map<string, string>();

// Dynamic SVG icons for different compact modes - увеличиваем размеры для лучшей читаемости
const ICONS = {
  folderOpen: {
    'normal': `<svg class="size-5" fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path></svg>`,
    'compact': `<svg class="size-4" fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path></svg>`,
    'ultra-compact': `<svg class="size-3.5" fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path></svg>`
  },
  folderClosed: {
    'normal': `<svg class="size-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg>`,
    'compact': `<svg class="size-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg>`,
    'ultra-compact': `<svg class="size-3.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg>`
  },
  code: {
    'normal': `<svg class="size-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>`,
    'compact': `<svg class="size-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>`,
    'ultra-compact': `<svg class="size-3.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>`
  },
  document: {
    'normal': `<svg class="size-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg>`,
    'compact': `<svg class="size-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg>`,
    'ultra-compact': `<svg class="size-3.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg>`
  },
  image: {
    'normal': `<svg class="size-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>`,
    'compact': `<svg class="size-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>`,
    'ultra-compact': `<svg class="size-3.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>`
  },
  chevron: {
    'normal': `<svg class="size-5 transition-transform duration-150 ease-out" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>`,
    'compact': `<svg class="size-4 transition-transform duration-150 ease-out" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>`,
    'ultra-compact': `<svg class="size-3.5 transition-transform duration-150 ease-out" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>`
  },
  spinner: {
    'normal': `<svg class="size-5 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>`,
    'compact': `<svg class="size-4 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>`,
    'ultra-compact': `<svg class="size-3.5 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>`
  }
} as const;

// Smart gitignore patterns for auto-selection - files/folders to EXCLUDE from RAG
const GITIGNORE_PATTERNS = [
  // Dependencies
  'node_modules', '.pnpm-store', 'bower_components', 'vendor', '__pycache__',
  // Build outputs
  'dist', 'build', 'out', '.next', '.nuxt', '.output', 'target', 'bin', 'obj', 'release',
  // Cache directories
  '.cache', '.parcel-cache', '.vite', '.turbo', '.webpack', '.pytest_cache',
  // IDE and editor files
  '.vscode', '.idea', '*.swp', '*.swo', '*~', '.DS_Store', 'Thumbs.db',
  // Logs
  '*.log', 'logs', 'npm-debug.log*', 'yarn-debug.log*', 'yarn-error.log*',
  // Runtime data
  'pids', '*.pid', '*.seed', '*.pid.lock',
  // Coverage directory used by tools like istanbul
  'coverage', '.nyc_output',
  // Dependency directories
  'jspm_packages',
  // Optional npm cache directory
  '.npm',
  // Optional eslint cache
  '.eslintcache',
  // Microbundle cache
  '.rpt2_cache', '.rts2_cache_cjs', '.rts2_cache_es', '.rts2_cache_umd',
  // Optional REPL history
  '.node_repl_history',
  // Output of 'npm pack'
  '*.tgz',
  // Yarn Integrity file
  '.yarn-integrity',
  // dotenv environment variables file
  '.env', '.env.local', '.env.development.local', '.env.test.local', '.env.production.local',
  // Stores VSCode versions used for testing VSCode extensions
  '.vscode-test',
  // Git
  '.git', '.gitignore',
  // OS generated files
  '.DS_Store', '.DS_Store?', '._*', '.Spotlight-V100', '.Trashes', 'ehthumbs.db', 'Thumbs.db',
  // Temporary files
  'tmp', 'temp', '*.tmp', '*.temp',
  // Additional common patterns
  '*.exe', '*.dll', '*.so', '*.dylib', '*.class', '*.jar', '*.war',
  // Lock files (usually not needed for RAG)
  'package-lock.json', 'yarn.lock', 'pnpm-lock.yaml', 'Pipfile.lock', 'poetry.lock'
] as const;

// Check if path should be ignored by default
const shouldIgnoreByDefault = (path: string, name: string): boolean => {
  const lowerName = name.toLowerCase();
  const lowerPath = path.toLowerCase();

  return GITIGNORE_PATTERNS.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(lowerName) || regex.test(lowerPath);
    }
    return lowerName === pattern || lowerPath.includes(`/${pattern}`) || lowerPath.includes(`\\${pattern}`);
  });
};

const getFileIcon = (fileName: string, isDirectory: boolean, isExpanded = false, compactMode: CompactMode = 'normal'): string => {
  if (isDirectory) {
    return isExpanded ? ICONS.folderOpen[compactMode] : ICONS.folderClosed[compactMode];
  }

  const ext = fileName.toLowerCase().split('.').pop() || '';
  const cacheKey = `icon_${ext}_${compactMode}`;

  if (!iconCache.has(cacheKey)) {
    let icon: string;

    // Categorize by file extension for optimal performance
    if (['js', 'ts', 'tsx', 'jsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala', 'dart'].includes(ext)) {
      icon = ICONS.code[compactMode];
    } else if (['png', 'jpg', 'jpeg', 'gif', 'svg', 'bmp', 'webp', 'ico', 'tiff', 'avif'].includes(ext)) {
      icon = ICONS.image[compactMode];
    } else if (['md', 'txt', 'json', 'yaml', 'yml', 'xml', 'csv', 'log', 'ini', 'conf', 'toml'].includes(ext)) {
      icon = ICONS.document[compactMode];
    } else {
      icon = ICONS.document[compactMode];
    }

    iconCache.set(cacheKey, icon);
  }

  return iconCache.get(cacheKey)!;
};

// Optimized color mapping with CSS custom properties
const getFileTypeColor = (fileName: string, isDirectory: boolean): string => {
  if (isDirectory) return 'text-primary-400';

  const ext = fileName.toLowerCase().split('.').pop() || '';

  // Use CSS custom properties for better performance
  switch (ext) {
    case 'js': case 'ts': case 'tsx': case 'jsx': return 'text-yellow-400';
    case 'py': return 'text-blue-400';
    case 'java': case 'kt': case 'scala': return 'text-orange-400';
    case 'cpp': case 'c': case 'cs': return 'text-blue-600';
    case 'css': case 'scss': case 'sass': case 'less': return 'text-blue-500';
    case 'html': case 'htm': return 'text-orange-500';
    case 'json': case 'yaml': case 'yml': case 'toml': return 'text-gray-400';
    case 'md': case 'mdx': return 'text-blue-300';
    case 'png': case 'jpg': case 'jpeg': case 'gif': case 'svg': case 'webp': return 'text-green-400';
    case 'rs': return 'text-orange-600';
    case 'go': return 'text-cyan-400';
    case 'php': return 'text-purple-400';
    case 'rb': return 'text-red-400';
    case 'swift': return 'text-orange-300';
    case 'dart': return 'text-blue-500';
    default: return 'text-gray-500';
  }
};

// ========================
// Ultra-Optimized TreeNode Component
// ========================
const TreeNode = React.memo((
  {
    node,
    style,
    onToggle,
    onSelect,
    selectionState,
    rowIndex,
    isActive,
    loadingNodes,
    compactMode,
    showCheckboxes,
  }: TreeNodeProps
) => {
  const [isHovered, setIsHovered] = useState(false);
  const isDir = !!node.hasChildren;
  const isLoading = loadingNodes.has(node.id);
  const iconSvg = getFileIcon(node.name, isDir, node.isExpanded, compactMode);
  const colorClass = getFileTypeColor(node.name, isDir);

  // Optimized event handlers with proper event delegation
  const handleToggle = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      if (isDir && !isLoading) {
        onToggle(node);
      }
    },
    [isDir, isLoading, onToggle, node]
  );

  const handleSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      e.stopPropagation();
      onSelect(node);
    },
    [onSelect, node]
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        if (e.key === ' ') {
          handleSelect(e as any);
        } else {
          handleToggle(e as any);
        }
      }
    },
    [handleSelect, handleToggle]
  );

  // Dynamic style calculations based on compact mode - увеличиваем размеры для лучшей читаемости
  const paddingLeft = useMemo(() => {
    const config = {
      'normal': { baseIndent: 8, levelIndent: 20 },
      'compact': { baseIndent: 6, levelIndent: 16 },
      'ultra-compact': { baseIndent: 4, levelIndent: 12 }
    };
    const { baseIndent, levelIndent } = config[compactMode];
    return `${baseIndent + node.level * levelIndent}px`;
  }, [node.level, compactMode]);

  const containerClasses = useMemo(() => {
    const config = {
      'normal': { padding: "px-2 py-1.5", radius: "rounded-sm" },
      'compact': { padding: "px-2 py-1", radius: "rounded-sm" },
      'ultra-compact': { padding: "px-1.5 py-0.5", radius: "rounded-sm" }
    };
    const { padding, radius } = config[compactMode];
    const base = `flex items-center ${padding} cursor-pointer ${radius} mx-1 transition-ultra-fast gpu-layer focus-ring`;

    if (isActive) {
      return `${base} bg-primary-500/20 border border-primary-500/50 shadow-sm`;
    }
    if (isHovered) {
      return `${base} bg-gray-800/40 hover:bg-gray-800/60 border border-gray-700/30`;
    }
    return `${base} border border-transparent hover:bg-gray-800/30`;
  }, [isActive, isHovered, compactMode]);

  return (
    <div
      style={style}
      className={`w-full tree-node gpu-boost ${isActive ? 'relative z-20' : 'relative z-10'}`}
      data-rowindex={rowIndex}
      tabIndex={0}
      role="treeitem"
      aria-expanded={isDir ? node.isExpanded : undefined}
      aria-level={node.level + 1}
      aria-selected={selectionState === 'all'}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onKeyDown={handleKeyDown}
    >
      <div
        className={containerClasses}
        style={{ paddingLeft }}
        onClick={handleToggle}
      >
        {/* Conditional Selection Checkbox with dynamic sizing */}
        {showCheckboxes && (
          <div className={`relative group ${compactMode === 'ultra-compact' ? 'mr-1.5' : compactMode === 'compact' ? 'mr-2' : 'mr-2.5'}`}>
            <input
              type="checkbox"
              checked={selectionState === 'all'}
              ref={(input) => {
                if (input) input.indeterminate = selectionState === 'some';
              }}
              onChange={handleSelect}
              onClick={(e) => e.stopPropagation()}
              className="checkbox-mini focus-ring"
              aria-label={`${selectionState === 'all' ? 'Deselect' : 'Select'} ${node.name}`}
            />
          </div>
        )}

        {/* Dynamic Expand/Collapse Icon */}
        {isDir && (
          <div
            className={`
              flex items-center justify-center rounded-sm cursor-pointer gpu-layer
              transition-ultra-fast hover:bg-gray-700/40
              ${compactMode === 'ultra-compact' ? 'size-3.5 mr-1' : compactMode === 'compact' ? 'size-4 mr-1' : 'size-5 mr-1.5'}
            `}
            onClick={handleToggle}
          >
            {isLoading ? (
              <div dangerouslySetInnerHTML={{ __html: ICONS.spinner[compactMode] }} />
            ) : (
              <div
                className={`
                  text-gray-400 transition-transform duration-150 ease-out
                  ${node.isExpanded ? 'rotate-90' : 'rotate-0'}
                `}
                dangerouslySetInnerHTML={{ __html: ICONS.chevron[compactMode] }}
              />
            )}
          </div>
        )}

        {/* Dynamic File/Folder Icon */}
        <div
          className={`
            flex items-center transition-transform duration-150 ease-out gpu-layer
            ${compactMode === 'ultra-compact' ? 'mr-1' : compactMode === 'compact' ? 'mr-1.5' : 'mr-2'}
            ${isHovered ? 'scale-105' : 'scale-100'}
          `}
        >
          <div
            className={`
              ${colorClass} transition-ultra-fast
              ${isActive ? 'brightness-110 drop-shadow-sm' : ''}
            `}
            dangerouslySetInnerHTML={{ __html: iconSvg }}
          />
        </div>

        {/* Dynamic File/Folder Name */}
        <span
          className={`
            flex-1 select-none whitespace-nowrap overflow-hidden text-ellipsis
            transition-ultra-fast font-medium leading-tight
            ${compactMode === 'ultra-compact' ? 'text-sm' : compactMode === 'compact' ? 'text-sm' : 'text-base'}
            ${isActive
              ? 'text-primary-300 drop-shadow-sm'
              : 'text-gray-200'
            }
          `}
          title={node.name}
        >
          {node.name}
        </span>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison for better performance
  return (
    prevProps.node.id === nextProps.node.id &&
    prevProps.node.isExpanded === nextProps.node.isExpanded &&
    prevProps.selectionState === nextProps.selectionState &&
    prevProps.isActive === nextProps.isActive &&
    prevProps.compactMode === nextProps.compactMode &&
    prevProps.showCheckboxes === nextProps.showCheckboxes &&
    prevProps.loadingNodes.has(prevProps.node.id) === nextProps.loadingNodes.has(nextProps.node.id)
  );
});

// ========================
// Ultra-Optimized Main FileTree Component
// ========================
export const FileTree = ({
  project,
  onSelectionChange,
  compactMode = 'compact',
  showCheckboxes = true
}: FileTreeProps) => {
  const [nodes, setNodes] = useState<Record<string, FileTreeNode[]>>({});
  const [loading, setLoading] = useState(false);
  const [loadingNodes, setLoadingNodes] = useState<Set<string>>(new Set());
  const [activeNodeId, setActiveNodeId] = useState<string | null>(null);
  const parentRef = useRef<HTMLDivElement>(null);
  const lastScrollTop = useRef(0);

  // Optimized state management with smart caching
  const [expanded, setExpanded] = useLocalStorage<string[]>(
    `fileTree-expanded-${project?.path || 'default'}`,
    []
  );
  const [selection, setSelection] = useLocalStorage<Record<string, 'all' | 'none' | 'some'>>(
    `fileTree-selection-${project?.path || 'default'}`,
    {}
  );

  const expandedSet = useMemo(() => new Set(expanded), [expanded]);

  // Ultra-optimized node flattening with smart memoization and caching
  const { nodeMap, flatNodes } = useMemo(() => {
    const cacheKey = `flatten_${project?.path}_${Object.keys(nodes).join(',')}_${expanded.join(',')}`;
    const cached = nodeCache.get(cacheKey);

    if (cached) {
      return cached;
    }

    const nodeMap = new Map<string, TreeNodeData>();
    const flatNodes: TreeNodeData[] = [];

    function flatten(parentId: string, level: number) {
      const children = nodes[parentId] || [];
      for (const node of children) {
        const isExpanded = expandedSet.has(node.id);
        const treeNode: TreeNodeData = {
          ...node,
          level,
          isExpanded,
          parentId: parentId === 'root' ? null : parentId,
        };
        nodeMap.set(node.id, treeNode);
        flatNodes.push(treeNode);

        // Only recurse if expanded and has children
        if (isExpanded && node.hasChildren && nodes[node.id]) {
          flatten(node.id, level + 1);
        }
      }
    }

    if (nodes.root) {
      flatten('root', 0);
    }

    const result = { nodeMap, flatNodes };
    nodeCache.set(cacheKey, result);
    return result;
  }, [nodes, expandedSet, project?.path, expanded]);

  // Optimized project initialization with smart preloading and auto-selection
  useEffect(() => {
    if (!project?.path) {
      setNodes({});
      setExpanded([]);
      setSelection({});
      setActiveNodeId(null);
      nodeCache.clear(); // Clear cache when project changes
      return;
    }

    setLoading(true);

    // Use RAF for smooth loading
    requestAnimationFrame(() => {
      window.electronAPI
        .readDirectoryLazy(project.path)
        .then((rootNodes) => {
          if (!rootNodes) return;

          setNodes({ root: rootNodes });

          // Initialize selection with smart defaults - only select files that are not in gitignore patterns
          // This runs asynchronously to avoid blocking the UI
          setTimeout(async () => {
            try {
              console.log('[FileTree] Starting auto-selection for project:', project.path);
              const initialSelection = await autoSelectFilesRecursively('root');
              console.log('[FileTree] Auto-selection completed:', Object.keys(initialSelection).length, 'items processed');
              setSelection(initialSelection);
            } catch (error) {
              console.warn('Failed to auto-select files:', error);
              // Fallback to simple selection - SELECT ALL by default except gitignore patterns
              const fallbackSelection: Record<string, 'all' | 'none' | 'some'> = {};
              rootNodes.forEach(node => {
                const shouldIgnore = shouldIgnoreByDefault(node.id, node.name);
                // SELECT by default, IGNORE only if in gitignore patterns
                fallbackSelection[node.id] = shouldIgnore ? 'none' : 'all';
              });
              console.log('[FileTree] Using fallback selection - selected:',
                Object.entries(fallbackSelection).filter(([_, state]) => state === 'all').length,
                'ignored:', Object.entries(fallbackSelection).filter(([_, state]) => state === 'none').length);
              setSelection(fallbackSelection);
            }
          }, 100); // Small delay to let UI render first

          // Don't auto-expand any directories - let users explore manually
          // This keeps the tree compact and lets users control what they see
        })
        .catch((error) => {
          console.error('Failed to load directory:', error);
          setNodes({});
        })
        .finally(() => setLoading(false));
    });
  }, [project?.path, setExpanded, setSelection]);

  // Ultra-fast debounced selection propagation with RAF
  const debouncedEmitSelection = useDebouncedCallback(
    (files: Set<string>) => onSelectionChange(files),
    100 // Reduced delay for faster response
  );

  // Optimized selection tracking with memoization
  const prevSelectionRef = useRef<string>('');
  const selectedFiles = useMemo(() => {
    const files = new Set<string>();
    for (const [id, state] of Object.entries(selection)) {
      if (state === 'all') {
        const node = nodeMap.get(id);
        if (node && !node.hasChildren) {
          files.add(id);
        }
      }
    }
    return files;
  }, [selection, nodeMap]);

  useEffect(() => {
    const selectionKey = Array.from(selectedFiles).sort().join(',');
    if (selectionKey !== prevSelectionRef.current) {
      prevSelectionRef.current = selectionKey;
      debouncedEmitSelection(selectedFiles);
    }
  }, [selectedFiles, debouncedEmitSelection]);

  // Optimized node toggle with loading states
  const toggleNode = useCallback(
    async (node: TreeNodeData) => {
      if (!node.hasChildren) return;

      const isCurrentlyExpanded = expandedSet.has(node.id);
      
      // Update expanded state immediately for responsive UI
      setExpanded((prev) => {
        const newExpanded = prev.filter((id) => id !== node.id);
        if (!isCurrentlyExpanded) {
          newExpanded.push(node.id);
        }
        return newExpanded;
      });

      // Load children if not already loaded and expanding
      if (!isCurrentlyExpanded && !nodes[node.id]) {
        setLoadingNodes((prev) => new Set([...prev, node.id]));
        
        try {
          const childNodes = await window.electronAPI.readDirectoryLazy(node.id);
          if (childNodes) {
            setNodes((prev) => ({ ...prev, [node.id]: childNodes }));
          }
        } catch (error) {
          console.error('Failed to load directory children:', error);
          // Revert expansion on error
          setExpanded((prev) => prev.filter((id) => id !== node.id));
        } finally {
          setLoadingNodes((prev) => {
            const newSet = new Set(prev);
            newSet.delete(node.id);
            return newSet;
          });
        }
      }
    },
    [expandedSet, nodes, setExpanded]
  );

  // Enhanced selection logic - SELECT ALL by default except gitignore patterns
  const autoSelectFilesRecursively = useCallback(async (nodeId: string, selection: Record<string, 'all' | 'none' | 'some'> = {}): Promise<Record<string, 'all' | 'none' | 'some'>> => {
    const children = nodes[nodeId] || [];

    // For root level, process immediate children and load first level directories
    if (nodeId === 'root') {
      console.log('[FileTree] Processing root level with', children.length, 'children');

      for (const child of children) {
        const shouldIgnore = shouldIgnoreByDefault(child.id, child.name);
        console.log('[FileTree] Processing:', child.name, 'shouldIgnore:', shouldIgnore, 'hasChildren:', child.hasChildren);

        if (!child.hasChildren) {
          // For files: SELECT by default, IGNORE only if in gitignore patterns
          selection[child.id] = shouldIgnore ? 'none' : 'all';
        } else {
          // For directories: load their children to determine selection state
          try {
            const dirChildren = await window.electronAPI.readDirectoryLazy(child.id);
            if (dirChildren && dirChildren.length > 0) {
              // Update nodes with loaded children
              setNodes(prev => ({ ...prev, [child.id]: dirChildren }));

              // Process directory children recursively
              const dirSelection = await autoSelectFilesRecursively(child.id, {});
              Object.assign(selection, dirSelection);

              // Determine directory state based on children
              const childFiles = dirChildren.filter(c => !c.hasChildren);
              const childDirs = dirChildren.filter(c => c.hasChildren);

              let allChildrenSelected = true;
              let noChildrenSelected = true;

              // Check files in this directory
              for (const file of childFiles) {
                const fileState = selection[file.id] || 'none';
                if (fileState === 'all') noChildrenSelected = false;
                if (fileState === 'none') allChildrenSelected = false;
              }

              // Check subdirectories
              for (const dir of childDirs) {
                const dirState = selection[dir.id] || 'none';
                if (dirState === 'all' || dirState === 'some') noChildrenSelected = false;
                if (dirState === 'none' || dirState === 'some') allChildrenSelected = false;
              }

              // Set directory state based on children
              if (shouldIgnore) {
                // If directory itself should be ignored, mark as none
                selection[child.id] = 'none';
              } else if (allChildrenSelected && !noChildrenSelected) {
                selection[child.id] = 'all';
              } else if (noChildrenSelected && !allChildrenSelected) {
                selection[child.id] = 'none';
              } else {
                selection[child.id] = 'some';
              }
            } else {
              // Empty directory - select if not in gitignore
              selection[child.id] = shouldIgnore ? 'none' : 'all';
            }
          } catch (error) {
            console.warn('Failed to load directory for auto-selection:', child.id, error);
            // On error, select if not in gitignore patterns
            selection[child.id] = shouldIgnore ? 'none' : 'all';
          }
        }
      }

      console.log('[FileTree] Root level selection completed:', Object.entries(selection).map(([id, state]) => `${id.split(/[/\\]/).pop()}: ${state}`));
      return selection;
    }

    // For non-root nodes, process children - SELECT ALL by default except gitignore
    console.log('[FileTree] Processing non-root node:', nodeId, 'with', children.length, 'children');

    for (const child of children) {
      const shouldIgnore = shouldIgnoreByDefault(child.id, child.name);

      if (!child.hasChildren) {
        // For files: SELECT by default, IGNORE only if in gitignore patterns
        selection[child.id] = shouldIgnore ? 'none' : 'all';
        console.log('[FileTree] File:', child.name, 'selected:', !shouldIgnore);
      } else {
        // For directories: SELECT by default unless in gitignore patterns
        // Don't load deeper levels automatically to keep performance good
        selection[child.id] = shouldIgnore ? 'none' : 'all';
        console.log('[FileTree] Directory:', child.name, 'selected:', !shouldIgnore);
      }
    }

    return selection;
  }, [nodes]);

  const handleSelect = useCallback(
    (node: TreeNodeData) => {
      setSelection((prev) => {
        const next = { ...prev };
        const currentState = next[node.id] || 'none';
        const newState = currentState === 'all' ? 'none' : 'all';

        // Apply to node and all descendants
        const applyToDescendants = (nodeId: string, state: 'all' | 'none') => {
          next[nodeId] = state;
          const children = nodes[nodeId] || [];
          children.forEach((child) => applyToDescendants(child.id, state));
        };

        applyToDescendants(node.id, newState);

        // Propagate up to ancestors
        let current = node.parentId ? nodeMap.get(node.parentId) : null;
        while (current) {
          const siblings = nodes[current.parentId || 'root'] || [];
          const siblingStates = siblings.map((s) => next[s.id] || 'none');

          if (siblingStates.every((s) => s === 'all')) {
            next[current.id] = 'all';
          } else if (siblingStates.every((s) => s === 'none')) {
            next[current.id] = 'none';
          } else {
            next[current.id] = 'some';
          }

          current = current.parentId ? nodeMap.get(current.parentId) : null;
        }

        return next;
      });
    },
    [nodes, nodeMap, setSelection]
  );

  // Enhanced keyboard navigation
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (!flatNodes.length) return;

      const activeElement = document.activeElement as HTMLElement;
      const currentIndex = activeElement?.dataset?.rowindex
        ? Number(activeElement.dataset.rowindex)
        : 0;

      let targetIndex = currentIndex;
      let handled = true;

      switch (e.key) {
        case 'ArrowDown':
          targetIndex = Math.min(currentIndex + 1, flatNodes.length - 1);
          break;
        case 'ArrowUp':
          targetIndex = Math.max(currentIndex - 1, 0);
          break;
        case 'ArrowRight': {
          const node = flatNodes[currentIndex];
          if (node?.hasChildren && !node.isExpanded) {
            toggleNode(node);
          } else {
            targetIndex = Math.min(currentIndex + 1, flatNodes.length - 1);
          }
          break;
        }
        case 'ArrowLeft': {
          const node = flatNodes[currentIndex];
          if (node?.hasChildren && node.isExpanded) {
            toggleNode(node);
          } else if (node?.parentId) {
            // Navigate to parent
            const parentIndex = flatNodes.findIndex((n: TreeNodeData) => n.id === node.parentId);
            if (parentIndex >= 0) targetIndex = parentIndex;
          }
          break;
        }
        case ' ':
        case 'Enter':
          e.preventDefault();
          const node = flatNodes[currentIndex];
          if (node) {
            if (e.key === ' ') {
              handleSelect(node);
            } else {
              toggleNode(node);
            }
          }
          break;
        case 'Home':
          targetIndex = 0;
          break;
        case 'End':
          targetIndex = flatNodes.length - 1;
          break;
        default:
          handled = false;
      }

      if (handled) {
        e.preventDefault();
        if (targetIndex !== currentIndex) {
          const targetElement = parentRef.current?.querySelector(
            `[data-rowindex="${targetIndex}"]`
          ) as HTMLElement;
          targetElement?.focus();
          setActiveNodeId(flatNodes[targetIndex]?.id || null);
        }
      }
    },
    [flatNodes, toggleNode, handleSelect]
  );

  // Dynamic virtualization with configurable height based on compact mode - увеличиваем высоту для лучшей читаемости
  const rowHeight = useMemo(() => {
    const heights = {
      'normal': 40,
      'compact': 32,
      'ultra-compact': 28
    };
    return heights[compactMode];
  }, [compactMode]);

  const rowVirtualizer = useVirtualizer({
    count: flatNodes.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => rowHeight,
    overscan: 8, // Optimized overscan for balance between performance and smoothness
    // Disable dynamic measurement for better performance
    measureElement: undefined,
    // Performance optimizations
    scrollMargin: 0,
    initialOffset: 0,
    // Enable smooth scrolling
    scrollPaddingStart: 0,
    scrollPaddingEnd: 0,
  });

  // Optimized loading state with GPU acceleration
  if (loading) {
    return (
      <div className="p-4 animate-fade-in">
        <div className="flex items-center mb-4 gpu-layer">
          <div className="size-4 mr-2" dangerouslySetInnerHTML={{ __html: ICONS.spinner[compactMode] }} />
          <span className="text-sm text-gray-400 font-medium">Loading project files...</span>
        </div>
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={i}
            className="h-6 bg-gray-800/40 rounded-sm mb-1 animate-pulse gpu-layer"
            style={{
              animationDelay: `${i * 0.05}s`,
              width: `${100 - i * 3}%`
            }}
          />
        ))}
      </div>
    );
  }

  // Optimized empty state
  if (!project) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6 text-center animate-fade-in gpu-layer">
        <div className="size-12 mb-4 text-gray-600" dangerouslySetInnerHTML={{ __html: ICONS.folderClosed.normal.replace('size-4', 'size-12') }} />
        <h3 className="text-lg font-semibold text-gray-400 mb-2">No Project Selected</h3>
        <p className="text-sm text-gray-500">Choose a project to explore its file structure</p>
      </div>
    );
  }

  // Optimized no files state
  if (!flatNodes.length && !loading) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6 text-center animate-fade-in gpu-layer">
        <div className="size-12 mb-4 text-gray-600" dangerouslySetInnerHTML={{ __html: ICONS.folderOpen.normal.replace('size-4', 'size-12') }} />
        <h3 className="text-lg font-semibold text-gray-400 mb-2">Empty Project</h3>
        <p className="text-sm text-gray-500">This project contains no files</p>
      </div>
    );
  }

  // Ultra-optimized main tree view with GPU acceleration
  return (
    <div
      ref={parentRef}
      className="
        h-full w-full overflow-y-auto overflow-x-hidden
        scrollbar-ultra-smooth tree-container
        focus-ring
      "
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="tree"
      aria-label="File tree"
      onScroll={(e) => {
        // Track scroll for potential optimizations
        lastScrollTop.current = e.currentTarget.scrollTop;
      }}
    >
      <div
        className="relative w-full gpu-boost"
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          contain: 'layout style paint'
        }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualItem) => {
          const node = flatNodes[virtualItem.index];
          if (!node) return null;

          const isActive = activeNodeId === node.id;

          return (
            <TreeNode
              key={`${node.id}-${virtualItem.index}`} // Ensure unique keys
              rowIndex={virtualItem.index}
              node={node}
              onToggle={toggleNode}
              onSelect={handleSelect}
              selectionState={selection[node.id] || 'none'}
              isActive={isActive}
              loadingNodes={loadingNodes}
              compactMode={compactMode}
              showCheckboxes={showCheckboxes}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualItem.size}px`,
                transform: `translate3d(0, ${virtualItem.start}px, 0)`, // Use translate3d for GPU acceleration
                contain: 'layout style paint',
              }}
            />
          );
        })}
      </div>
    </div>
  );
};
